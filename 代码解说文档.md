# STM32F103 双轴步进电机控制系统 - 代码解说文档

## 📋 项目概述

这是一个基于STM32F103ZE微控制器的双轴步进电机控制系统，主要用于精确的二维定位控制。系统通过串口接收目标位置信息，使用PID控制算法驱动两个步进电机实现精确定位。

## 🏗️ 系统架构

### 目录结构
```
Project/
├── User/           # 用户代码（主程序和配置）
├── Hardware/       # 硬件驱动模块
├── System/         # 系统级功能（定时器、延时）
├── Library/        # STM32标准外设库
├── Start/          # 启动文件和系统配置
└── Objects/        # 编译输出文件
```

## 🔧 核心模块详解

### 1. 主控制模块 (User/main.c)

**核心功能：**
- 系统初始化和主循环控制
- PID控制算法执行
- 步进电机方向和步数控制

**关键数据结构：**
```c
// 步进电机控制结构体
Motor_Step Step1 = {
    .remain_step = 0,  // 剩余步数
    .dir = 0,          // 方向控制
    .number = 0,       // 电机编号
    .enable = 1        // 使能标志
};

// PID控制器结构体
PID STEP_X_PID = {
    .Kp = 0.1,         // 比例系数
    .Ki = 0,           // 积分系数  
    .Kd = 0.2,         // 微分系数
    .Target = 230,     // 目标位置
    .OutMax = 10,      // 输出上限
    .OutMin = -10      // 输出下限
};
```

**工作流程：**
1. 系统初始化（定时器、GPIO、PWM、串口）
2. 主循环中执行PID计算
3. 根据PID输出设置电机方向和步数
4. 通过定时器中断控制电机运行

### 2. 步进电机驱动模块 (Hardware/bjbj_driver.c)

**主要功能：**
- GPIO引脚配置和控制
- 步进电机细分设置
- 电机方向和使能控制

**引脚定义：**
```c
// 第一个电机控制引脚
#define EN_Pin    GPIO_Pin_12    // 使能引脚
#define DIR_Pin   GPIO_Pin_15    // 方向引脚
#define MS1_Pin   GPIO_Pin_14    // 细分控制1
#define MS2_Pin   GPIO_Pin_0     // 细分控制2

// 第二个电机控制引脚
#define EN_Pin1   GPIO_Pin_0     // 使能引脚
#define DIR_Pin1  GPIO_Pin_1     // 方向引脚
```

**关键函数：**
- `SubdivisionSet()`: 设置32细分模式
- `Control_Step()`: 控制电机方向和使能
- `GPIO_Config()`: 初始化所有控制引脚

### 3. PID控制模块 (Hardware/PID.c)

**算法实现：**
```c
void PID_Postion(PID *PID1) {
    PID1->Error1 = PID1->Error0;                    // 保存上次误差
    PID1->Error0 = PID1->Target - PID1->Actual;     // 计算当前误差
    
    // 积分项处理
    if (PID1->Ki != 0) {
        PID1->ErrorInt += PID1->Error0;
    }
    
    // PID输出计算
    PID1->Out = PID1->Kp * PID1->Error0 +           // 比例项
                PID1->Ki * PID1->ErrorInt +          // 积分项
                PID1->Kd * (PID1->Error0 - PID1->Error1); // 微分项
    
    // 输出限幅
    if (PID1->Out > PID1->OutMax) PID1->Out = PID1->OutMax;
    if (PID1->Out < PID1->OutMin) PID1->Out = PID1->OutMin;
}
```

### 4. PWM脉冲生成模块 (Hardware/PWM.c)

**功能说明：**
- 配置TIM2和TIM3产生步进电机所需的脉冲信号
- 通过定时器中断控制脉冲数量
- 支持动态启停控制

**配置参数：**
- 定时器频率：1MHz (72MHz/72分频)
- PWM周期：500μs
- 占空比：50%

### 5. 串口通信模块 (Hardware/Serial.c)

**通信协议：**
- 波特率：115200
- 帧格式：0xFF + 数据 + 0xFE
- 支持两种数据格式：
  - 简单坐标：6字节 (x坐标 + y坐标)
  - 四点坐标：17字节 (4个点的x,y坐标)

**数据解析：**
```c
// 简单坐标解析
blob_cx = uart1_rx_buf[1] * 100 + uart1_rx_buf[2];
blob_cy = uart1_rx_buf[3] * 100 + uart1_rx_buf[4];

// 四点坐标解析
rect_34[0][0] = uart1_rx_buf[1] * 100 + uart1_rx_buf[2];  // 第一个点x坐标
rect_34[0][1] = uart1_rx_buf[3] * 100 + uart1_rx_buf[4];  // 第一个点y坐标
```

### 6. 定时器系统 (System/Timer.c)

**TIM6配置：**
- 1ms定时中断
- 用于按键扫描和目标点切换
- 步进电机控制信号生成

**中断处理：**
- 按键检测：每50ms扫描一次
- 目标切换：每3秒切换一个目标点
- 电机控制：实时调用Control_Step函数

## 🔄 系统工作原理

### 整体控制流程

1. **初始化阶段**
   - 配置系统时钟和外设
   - 初始化GPIO、定时器、串口
   - 设置PID参数和目标位置

2. **数据接收**
   - 串口接收目标坐标数据
   - 解析并存储到全局变量
   - 更新PID控制器目标值

3. **位置控制**
   - 读取当前位置(blob_cx, blob_cy)
   - PID算法计算控制输出
   - 转换为电机步数和方向

4. **电机驱动**
   - 设置电机方向和剩余步数
   - 启动对应的PWM定时器
   - 通过中断逐步减少剩余步数

5. **循环执行**
   - 40ms周期性执行控制算法
   - 实时响应位置变化
   - 自动切换目标点

### 双轴协调控制

系统同时控制X轴和Y轴两个步进电机：
- **X轴电机(Step1)**：控制水平方向移动
- **Y轴电机(Step2)**：控制垂直方向移动
- **独立PID控制**：每个轴都有独立的PID控制器
- **同步执行**：两轴可以同时运动实现斜向移动

## 🎯 关键特性

1. **高精度定位**：32细分步进电机驱动
2. **实时控制**：1ms定时中断响应
3. **智能算法**：PID闭环控制系统
4. **灵活通信**：串口数据接收和解析
5. **安全保护**：输出限幅和错误处理

## 📊 性能参数

- **控制精度**：步进电机32细分
- **响应时间**：40ms控制周期
- **通信速率**：115200 bps
- **定位范围**：0-999坐标范围
- **控制方式**：双轴独立PID控制

这个系统特别适用于需要精确二维定位的应用场景，如激光雕刻、3D打印平台、自动化装配等领域。

## 🔍 详细技术分析

### 中断系统设计

系统使用了多个中断来实现实时控制：

**TIM6中断 (1ms周期)**
```c
void TIM6_IRQHandler(void) {
    // 按键扫描 - 每50ms执行一次
    if(++key_count == 50) {
        key_count = 0;
        key_value = Key_read();
        key_down = key_value &(key_old ^key_value);  // 检测按键按下沿
        key_old = key_value;
    }

    // 目标点切换 - 每3秒切换一次
    if(++test1_count==3000) {
        test1_count =0;
        STEP_X_PID.Target = rect_34[first_barr_count][0];  // 更新X轴目标
        STEP_Y_PID.Target = rect_34[first_barr_count][1];  // 更新Y轴目标
        if(++first_barr_count==4) first_barr_count=0;     // 循环切换4个点
    }

    // 电机控制信号生成
    Control_Step(&Step1);  // X轴电机控制
    Control_Step(&Step2);  // Y轴电机控制
}
```

**TIM2/TIM3中断 (步进脉冲控制)**
```c
void TIM2_IRQHandler(void) {  // X轴电机脉冲控制
    if(Step1.remain_step > 0) {
        Step1.remain_step--;      // 减少剩余步数
    } else {
        TIM_Cmd(TIM2, DISABLE);   // 停止定时器
    }
}
```

### 数据流向分析

```
串口数据接收 → 坐标解析 → PID目标更新 → PID计算 → 电机控制 → 脉冲输出
     ↑              ↓              ↓           ↓          ↓
  外部设备      全局变量存储    误差计算    方向/步数    GPIO输出
```

**数据传递路径：**
1. **外部输入** → `USART1_IRQHandler()` → `blob_cx/blob_cy` 或 `rect_34[][]`
2. **目标设定** → `STEP_X_PID.Target` / `STEP_Y_PID.Target`
3. **实际位置** → `STEP_X_PID.Actual` / `STEP_Y_PID.Actual`
4. **PID输出** → `STEP_X_PID.Out` / `STEP_Y_PID.Out`
5. **电机控制** → `Step1.dir` / `Step1.remain_step`

### 硬件接口说明

**步进电机驱动器接口：**
```
电机1 (X轴):
- EN_Pin (PF12)   → 使能信号 (低电平有效)
- DIR_Pin (PF15)  → 方向信号 (高/低电平控制正反转)
- MS1_Pin (PF14)  → 细分控制位1
- MS2_Pin (PG0)   → 细分控制位2
- STEP信号        → TIM2_CH1 (PA0) PWM输出

电机2 (Y轴):
- EN_Pin1 (PF0)   → 使能信号
- DIR_Pin1 (PF1)  → 方向信号
- MS1_Pin1 (PF2)  → 细分控制位1
- MS2_Pin1 (PF3)  → 细分控制位2
- STEP信号        → TIM3_CH1 (PA6) PWM输出
```

**串口通信接口：**
```
USART1:
- TX (PA9)  → 数据发送
- RX (PA10) → 数据接收
- 波特率: 115200
- 数据位: 8
- 停止位: 1
- 校验位: 无
```

### PID参数调优指南

**当前PID参数：**
```c
// X轴PID参数
STEP_X_PID.Kp = 0.1;    // 比例系数 - 控制响应速度
STEP_X_PID.Ki = 0;      // 积分系数 - 消除稳态误差
STEP_X_PID.Kd = 0.2;    // 微分系数 - 减少超调

// Y轴PID参数
STEP_Y_PID.Kp = 0.1;
STEP_Y_PID.Ki = 0;
STEP_Y_PID.Kd = 0.2;
```

**调优建议：**
1. **Kp调节**：增大Kp提高响应速度，但过大会导致震荡
2. **Ki调节**：当前为0，如需消除稳态误差可适当增加
3. **Kd调节**：当前0.2，有助于减少超调和震荡
4. **输出限制**：±10步/周期，防止电机过载

### 通信协议详解

**帧格式1 - 简单坐标 (6字节):**
```
0xFF | X高位 | X低位 | Y高位 | Y低位 | 0xFE
```

**帧格式2 - 四点坐标 (17字节):**
```
0xFF | X0高 | X0低 | Y0高 | Y0低 | X1高 | X1低 | Y1高 | Y1低 |
     | X2高 | X2低 | Y2高 | Y2低 | X3高 | X3低 | Y3高 | Y3低 | 0xFE
```

**坐标计算方式：**
```c
// 坐标值 = 高位字节 × 100 + 低位字节
// 例如：坐标230 → 高位=2, 低位=30
int coordinate = high_byte * 100 + low_byte;
```

## 🛠️ 使用说明

### 编译环境
- **IDE**: Keil MDK-ARM
- **编译器**: ARMCC v5.06
- **目标芯片**: STM32F103ZE
- **标准库**: STM32F10x_StdPeriph_Lib

### 硬件连接
1. 连接步进电机驱动器到指定GPIO引脚
2. 连接串口到外部控制设备
3. 确保电源供电正常(3.3V/5V)
4. 检查步进电机驱动器细分设置

### 软件配置
1. 根据实际硬件调整GPIO引脚定义
2. 修改PID参数适应具体应用
3. 调整坐标范围和精度要求
4. 配置串口通信参数

### 调试建议
1. 使用串口输出调试信息
2. 监控PID输出值和误差
3. 检查电机运行方向是否正确
4. 验证坐标解析是否准确

## ⚠️ 注意事项

1. **电机驱动器设置**：确保细分模式与代码一致(32细分)
2. **电源管理**：步进电机需要足够的驱动电流
3. **中断优先级**：避免中断冲突影响实时性
4. **坐标范围**：确保目标坐标在有效范围内
5. **通信稳定性**：检查串口数据完整性

## 🔧 扩展功能建议

1. **位置反馈**：添加编码器实现闭环位置控制
2. **速度控制**：实现变速运动和S型加减速
3. **多点插补**：支持直线和圆弧插补
4. **故障检测**：添加限位开关和故障诊断
5. **参数存储**：使用EEPROM保存PID参数

## 🚀 快速入门指南

### 第一步：硬件准备
1. **主控板**：STM32F103ZE开发板
2. **步进电机**：两个步进电机(建议NEMA17)
3. **驱动器**：支持细分的步进电机驱动器(如A4988、DRV8825)
4. **电源**：12V/24V电源供电
5. **连接线**：杜邦线若干

### 第二步：硬件连接
```
步进电机驱动器1 (X轴):
VDD  → 3.3V
GND  → GND
EN   → PF12
DIR  → PF15
STEP → PA0 (TIM2_CH1)
MS1  → PF14
MS2  → PG0

步进电机驱动器2 (Y轴):
VDD  → 3.3V
GND  → GND
EN   → PF0
DIR  → PF1
STEP → PA6 (TIM3_CH1)
MS1  → PF2
MS2  → PF3

串口连接:
TX → PA9
RX → PA10
GND → GND

按键连接:
KEY → PD0
GND → GND
```

### 第三步：软件配置
1. **打开Keil工程**：双击`Project.uvprojx`
2. **编译工程**：按F7编译，确保无错误
3. **下载程序**：连接ST-Link，按F8下载
4. **串口调试**：打开串口助手，波特率115200

### 第四步：功能测试
1. **基本测试**：上电后LED应该正常闪烁
2. **串口测试**：发送测试数据验证通信
3. **电机测试**：观察电机是否能正常转动
4. **定位测试**：发送坐标数据测试定位精度

### 第五步：参数调优
1. **PID参数**：根据实际负载调整Kp、Ki、Kd
2. **速度设置**：修改PWM频率调整电机速度
3. **精度调整**：根据需要修改细分设置
4. **坐标范围**：设置合适的工作范围

## 📝 常见问题解答

**Q1: 电机不转动怎么办？**
A1: 检查使能信号、电源供电、驱动器连接和细分设置

**Q2: 定位精度不够怎么办？**
A2: 调整PID参数，增加细分数，检查机械间隙

**Q3: 串口通信异常怎么办？**
A3: 检查波特率设置、数据格式、连线是否正确

**Q4: 系统运行不稳定怎么办？**
A4: 检查电源质量、中断优先级设置、代码逻辑

**Q5: 如何修改坐标范围？**
A5: 修改PID目标值范围和串口数据解析范围

## 📚 相关资源

- **STM32参考手册**：STM32F103xx Reference Manual
- **标准外设库**：STM32F10x Standard Peripheral Library
- **步进电机原理**：步进电机控制技术手册
- **PID控制理论**：自动控制原理相关教材
- **Keil开发环境**：MDK-ARM用户指南

---

**总结**：这是一个功能完整的双轴步进电机控制系统，采用了现代嵌入式系统的典型架构设计。通过PID闭环控制、中断驱动、模块化编程等技术，实现了高精度、高实时性的二维定位控制。代码结构清晰，易于理解和扩展，是学习STM32和步进电机控制的优秀范例。
